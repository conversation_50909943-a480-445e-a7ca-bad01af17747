# 常用模式和最佳实践

- SysUserServiceImpl.selectUserList方法排序规则已优化：在SysUserMapper.xml中将排序从单一的create_time desc调整为status asc, create_time desc，确保正常状态(0)用户优先显示，停用状态(1)用户排在后面，相同状态内按创建时间降序排列
- OrderVideoMatchPreselectModelServiceImpl.selectMyPreselectDistributionListByCondition方法已优化：添加了modelIds和englishCustomerServiceIds的交集处理逻辑，当两个参数同时存在时计算交集，交集为空则直接返回空列表，提升了查询性能和业务逻辑准确性
- ModelController新增下拉筛选接口实现模式：1)在ModelMapper接口和XML中添加selectModelNameAccountOptions方法，使用LEFT JOIN关联model和model_account表，CONCAT拼接"姓名+账户名"格式，DISTINCT去重，ORDER BY排序，过滤status!=3；2)在IModelService接口和ModelServiceImpl中添加对应方法；3)在ModelController中添加GET接口，使用@RequiresPermissions("model:manage:list")权限控制，@LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)用户类型限制，返回R<List<String>>格式
- Liquibase changeset创建模式：在changelog-wym-1.0.yml中添加了完整的changeset（id: wym-**********），包含modifyDataType操作修改business_member_validity_flow表remark字段从varchar(300)到varchar(500)，并添加了rollback操作支持回滚到原始类型，遵循项目的Liquibase规范
- Liquibase changeset正确位置：business_member_validity_flow表相关的changeset应该添加到wnkx-business/db/changelog/changelog-wym-1.0.yml文件中，而不是ruoyi-modules/db/changelog/changelog-wym-1.0.yml文件中。已成功在正确位置创建changeset（id: wym-**********）修改remark字段类型
