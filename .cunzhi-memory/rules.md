# 开发规范和规则

- PageHelper 分页只对下一个 MyBatis 查询生效，如果有多个查询操作，需要确保分页应用到正确的查询上，或使用手动分页处理最终结果
- MySQL DISTINCT查询规则：当使用DISTINCT关键字时，ORDER BY子句中的字段必须出现在SELECT列表中，否则会报错"Expression #1 of ORDER BY clause is not in SELECT list"
- Spring数据绑定规则：Collection接口不支持索引参数绑定(如id[0])，需要使用具体类型List、数组或Map。ModelListDTO的id字段应使用List<Long>而非Collection<Long>以支持前端数组参数传递
- Liquibase setColumnRemarks操作在某些数据库版本中可能不生效，推荐使用SQL语句或modifyColumn来同时修改字段类型和注释，确保字段注释正确设置
